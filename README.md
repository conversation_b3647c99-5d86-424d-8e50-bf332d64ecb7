# 股票数据管理器

基于您原始代码改进的股票数据下载和缓存系统，专门用于获取特定时间点的股票收盘价数据，并整合Tushare数据源提供全面的股票数据分析。

## 主要功能

### 1. 数据缓存机制
- 自动检测已缓存的股票数据，避免重复下载
- 支持增量更新，只下载缺失的股票数据
- 数据以CSV格式保存，便于后续处理

### 2. 特定时间点数据提取
- 提取每日的关键时间点收盘价：
  - 09:35 收盘价
  - 09:40 收盘价
  - 09:45 收盘价
- 自动匹配最接近的时间点（容差3分钟内）

### 3. 交易日历支持
- 自动获取交易日历，过滤非交易日
- 支持指定日期范围的数据处理

## 文件结构

```
├── stock_data_manager.py        # xtquant数据管理类
├── tushare_data_manager.py      # tushare数据管理类
├── comprehensive_data_manager.py # 综合数据管理类
├── download_helper.py           # 下载辅助函数
├── example_usage.py             # 使用示例和演示
├── README.md                   # 说明文档
└── data/                       # 数据目录
    ├── cache/                  # xtquant原始数据缓存
    ├── tushare_cache/          # tushare数据缓存
    ├── comprehensive/          # 综合数据集
    ├── target_time_data_*.csv  # 目标时间点数据
    └── formatted_target_data_*.csv  # 格式化后的数据
```

## 使用方法

### 1. 基本使用 - xtquant数据

```python
from stock_data_manager import StockDataManager

# 创建数据管理器
manager = StockDataManager()

# 获取从20241108开始的目标时间点数据
target_data = manager.get_target_time_data_for_all_stocks(start_date='20241108')

print(f"获取到 {len(target_data)} 条数据")
```

### 2. Tushare数据缓存

```python
from tushare_data_manager import TushareDataManager

# 创建Tushare数据管理器
ts_manager = TushareDataManager()

# 缓存综合数据
data = ts_manager.cache_comprehensive_data(start_date='20241108')
```

### 3. 综合数据集创建

```python
from comprehensive_data_manager import ComprehensiveDataManager

# 创建综合数据管理器
manager = ComprehensiveDataManager()

# 创建包含所有字段的综合数据集
comprehensive_data = manager.create_comprehensive_dataset(start_date='20241108')
```

### 4. 运行示例

```bash
# 运行xtquant示例
python example_usage.py

# 运行Tushare示例
python tushare_data_manager.py

# 运行综合数据示例
python comprehensive_data_manager.py
```

## 主要改进

### 相比原始代码的优化：

1. **多数据源整合**
   - 整合xtquant和Tushare两个数据源
   - 提供全面的股票数据字段
   - 支持财务数据、资金流向、指数成分股等

2. **缓存机制**
   - 避免重复下载已有数据
   - 支持增量更新
   - 提高数据获取效率

3. **目标数据提取**
   - 专门提取09:35、09:40、09:45三个时间点的数据
   - 自动处理时间匹配和容差

4. **时间范围控制**
   - 支持指定起始时间（从20241108开始）
   - 增量下载机制
   - 避免下载不必要的历史数据

5. **错误处理**
   - 更完善的异常处理
   - 详细的进度提示
   - 数据验证机制

6. **数据格式化**
   - 提供多种数据输出格式
   - 便于后续分析和使用
   - 支持中文列名

## 配置说明

### 主要参数

- `token`: Tushare API token
- `dividend_type`: 复权类型，默认'front'（前复权）
- `period`: 数据周期，默认'5m'（5分钟）
- `target_times`: 目标时间点，默认['09:35:00', '09:40:00', '09:45:00']

### 自定义配置

```python
# 创建自定义配置的管理器
manager = StockDataManager(token='your_token')
manager.target_times = ['09:30:00', '09:35:00', '09:40:00']  # 自定义时间点
manager.period = '1m'  # 使用1分钟数据
```

## 数据输出格式

### 1. 原始目标时间点数据
```csv
date,target_time,actual_time,close,open,high,low,volume,code
2024-11-08,09:35:00,09:35:00,12.34,12.30,12.40,12.25,1000000,000001.SZ
```

### 2. 格式化数据（透视表格式）
```csv
date,code,close_0935,close_0940,close_0945
2024-11-08,000001.SZ,12.34,12.45,12.50
```

### 3. 综合数据集（包含所有字段）
```csv
股票代码,股票名称,交易日期,开盘价,最高价,最低价,收盘价,前收盘价,成交量,成交额,流通市值,总市值,净利润TTM,现金流TTM,净资产,总资产,总负债,净利润(当季),中户资金买入额,中户资金卖出额,大户资金买入额,大户资金卖出额,散户资金买入额,散户资金卖出额,机构资金买入额,机构资金卖出额,沪深300成分股,上证50成分股,中证500成分股,中证1000成分股,中证2000成分股,创业板指成分股,新版申万一级行业名称,新版申万二级行业名称,新版申万三级行业名称,09:35收盘价,09:40收盘价,09:45收盘价
000001.SZ,平安银行,20241108,12.30,12.45,12.25,12.40,12.35,1000000,12400000,240000000000,250000000000,,,,,,,1000000,900000,2000000,1800000,5000000,4800000,500000,600000,1,0,0,0,0,0,银行,股份制商业银行,股份制商业银行,12.34,12.38,12.42
```

## 注意事项

1. **API限制**: 请确保Tushare API token有效且有足够的调用次数
2. **网络连接**: 数据下载需要稳定的网络连接
3. **存储空间**: 大量股票数据需要足够的磁盘空间
4. **时间匹配**: 系统会自动匹配最接近的时间点，容差为3分钟

## 依赖包

```
pandas
xtquant
tushare
joblib
```

## 故障排除

### 常见问题

1. **Token错误**: 检查Tushare API token是否正确
2. **网络超时**: 检查网络连接，可能需要重试
3. **数据缺失**: 某些股票可能在特定时间点没有交易数据
4. **权限问题**: 确保有写入data目录的权限

### 调试模式

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 测试单只股票
manager.download_data_for_stock('000001.SZ', '5m')
```
