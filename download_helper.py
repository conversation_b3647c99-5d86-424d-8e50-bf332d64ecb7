"""
独立的数据下载辅助函数
解决并行处理中的pickle问题
"""
from xtquant import xtdata

def download_single_stock(stock_code, period='5m', start_time='20241108'):
    """
    下载单只股票数据的独立函数

    Args:
        stock_code: 股票代码
        period: 数据周期
        start_time: 起始时间

    Returns:
        tuple: (stock_code, success)
    """
    try:
        # 添加时间范围和增量下载参数
        xtdata.download_history_data(stock_code=stock_code, period=period,
                                   start_time=start_time, incrementally=True)
        return (stock_code, True)
    except Exception as e:
        print(f"{stock_code} 数据下载失败: {e}")
        return (stock_code, False)

def download_stocks_batch(stock_list, period='5m', batch_size=50, start_time='20241108'):
    """
    批量下载股票数据

    Args:
        stock_list: 股票代码列表
        period: 数据周期
        batch_size: 批次大小
        start_time: 起始时间

    Returns:
        list: 下载结果列表
    """
    results = []
    total = len(stock_list)

    for i in range(0, total, batch_size):
        batch = stock_list[i:i+batch_size]
        print(f"下载批次 {i//batch_size + 1}/{(total-1)//batch_size + 1}: {i+1}-{min(i+batch_size, total)}/{total}")

        for stock in batch:
            result = download_single_stock(stock, period, start_time)
            results.append(result)

        # 每个批次后稍作休息
        import time
        time.sleep(1)

    return results
