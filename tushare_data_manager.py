import pandas as pd
import tushare as ts
import os
import datetime
import warnings
warnings.filterwarnings("ignore")

class TushareDataManager:
    def __init__(self, token='2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211'):
        """
        Tushare数据管理器
        
        Args:
            token: tushare API token
        """
        self.token = token
        self.pro = ts.pro_api(token)
        self.cache_folder = 'data/tushare_cache'
        os.makedirs(self.cache_folder, exist_ok=True)
        
    def get_trading_dates(self, start_date='20241108', end_date=None):
        """
        获取交易日期列表
        
        Args:
            start_date: 开始日期 'YYYYMMDD'
            end_date: 结束日期 'YYYYMMDD'
        
        Returns:
            list: 交易日期列表
        """
        if end_date is None:
            end_date = datetime.datetime.now().strftime('%Y%m%d')
            
        try:
            cal = self.pro.trade_cal(exchange='SSE', start_date=start_date, end_date=end_date)
            trading_dates = cal[cal['is_open'] == 1]['cal_date'].tolist()
            return trading_dates
        except Exception as e:
            print(f"获取交易日历失败: {e}")
            return []
    
    def get_stock_basic_info(self):
        """
        获取股票基础信息
        
        Returns:
            DataFrame: 股票基础信息
        """
        try:
            # 获取股票基础信息
            stock_basic = self.pro.stock_basic(exchange='', list_status='L', 
                                             fields='ts_code,symbol,name,area,industry,market,list_date')
            return stock_basic
        except Exception as e:
            print(f"获取股票基础信息失败: {e}")
            return pd.DataFrame()
    
    def get_daily_basic_data(self, trade_date):
        """
        获取指定日期的每日基础数据
        
        Args:
            trade_date: 交易日期 'YYYYMMDD'
            
        Returns:
            DataFrame: 每日基础数据
        """
        try:
            # 获取每日基础数据
            daily_basic = self.pro.daily_basic(
                ts_code='', 
                trade_date=trade_date,
                fields='ts_code,trade_date,close,turnover_rate,volume_ratio,pe,pe_ttm,pb,ps,ps_ttm,dv_ratio,dv_ttm,total_share,float_share,free_share,total_mv,circ_mv'
            )
            return daily_basic
        except Exception as e:
            print(f"获取{trade_date}每日基础数据失败: {e}")
            return pd.DataFrame()
    
    def get_daily_price_data(self, trade_date):
        """
        获取指定日期的日线行情数据
        
        Args:
            trade_date: 交易日期 'YYYYMMDD'
            
        Returns:
            DataFrame: 日线行情数据
        """
        try:
            # 获取日线行情数据
            daily = self.pro.daily(
                ts_code='',
                trade_date=trade_date,
                fields='ts_code,trade_date,open,high,low,close,pre_close,change,pct_chg,vol,amount'
            )
            return daily
        except Exception as e:
            print(f"获取{trade_date}日线行情数据失败: {e}")
            return pd.DataFrame()
    
    def get_financial_data(self, period):
        """
        获取财务数据
        
        Args:
            period: 报告期 'YYYYMMDD'
            
        Returns:
            dict: 包含各种财务数据的字典
        """
        financial_data = {}
        
        try:
            # 利润表数据
            income = self.pro.income(period=period, fields='ts_code,end_date,n_income,n_cashflow_act')
            financial_data['income'] = income
            
            # 资产负债表数据  
            balancesheet = self.pro.balancesheet(period=period, fields='ts_code,end_date,total_assets,total_liab,total_hldr_eqy_exc_min_int')
            financial_data['balance'] = balancesheet
            
            print(f"获取{period}财务数据成功")
            
        except Exception as e:
            print(f"获取{period}财务数据失败: {e}")
            
        return financial_data
    
    def get_moneyflow_data(self, trade_date):
        """
        获取资金流向数据
        
        Args:
            trade_date: 交易日期 'YYYYMMDD'
            
        Returns:
            DataFrame: 资金流向数据
        """
        try:
            # 获取资金流向数据
            moneyflow = self.pro.moneyflow(
                ts_code='',
                trade_date=trade_date,
                fields='ts_code,trade_date,buy_sm_vol,buy_sm_amount,sell_sm_vol,sell_sm_amount,buy_md_vol,buy_md_amount,sell_md_vol,sell_md_amount,buy_lg_vol,buy_lg_amount,sell_lg_vol,sell_lg_amount,buy_elg_vol,buy_elg_amount,sell_elg_vol,sell_elg_amount'
            )
            return moneyflow
        except Exception as e:
            print(f"获取{trade_date}资金流向数据失败: {e}")
            return pd.DataFrame()
    
    def get_index_weight_data(self):
        """
        获取指数成分股权重数据
        
        Returns:
            dict: 各指数成分股数据
        """
        index_data = {}
        
        # 主要指数代码
        index_codes = {
            '000300.SH': '沪深300',
            '000016.SH': '上证50', 
            '000905.SH': '中证500',
            '000852.SH': '中证1000',
            '932000.CSI': '中证2000',
            '399006.SZ': '创业板指'
        }
        
        for index_code, index_name in index_codes.items():
            try:
                # 获取指数成分股
                index_weight = self.pro.index_weight(index_code=index_code)
                if not index_weight.empty:
                    index_data[index_name] = index_weight
                    print(f"获取{index_name}成分股数据成功")
            except Exception as e:
                print(f"获取{index_name}成分股数据失败: {e}")
                
        return index_data
    
    def get_industry_classification(self):
        """
        获取申万行业分类数据
        
        Returns:
            DataFrame: 申万行业分类数据
        """
        try:
            # 获取申万行业分类
            sw_industry = self.pro.stock_basic(
                exchange='', 
                list_status='L',
                fields='ts_code,name,industry'
            )
            
            # 获取详细的申万行业分类
            sw_class = self.pro.ths_index(exchange='A', type='N')
            
            return sw_industry, sw_class
        except Exception as e:
            print(f"获取申万行业分类失败: {e}")
            return pd.DataFrame(), pd.DataFrame()
    
    def cache_comprehensive_data(self, start_date='20241108', end_date=None):
        """
        缓存综合数据
        
        Args:
            start_date: 开始日期 'YYYYMMDD'
            end_date: 结束日期 'YYYYMMDD'
        """
        print("开始缓存Tushare综合数据...")
        
        # 获取交易日期
        trading_dates = self.get_trading_dates(start_date, end_date)
        if not trading_dates:
            print("未获取到交易日期")
            return
            
        print(f"需要处理 {len(trading_dates)} 个交易日")
        
        # 获取股票基础信息
        print("获取股票基础信息...")
        stock_basic = self.get_stock_basic_info()
        if stock_basic.empty:
            print("未获取到股票基础信息")
            return
            
        # 获取指数成分股数据
        print("获取指数成分股数据...")
        index_data = self.get_index_weight_data()
        
        # 获取行业分类数据
        print("获取行业分类数据...")
        sw_industry, sw_class = self.get_industry_classification()
        
        # 按日期处理数据
        all_data = []
        
        for i, trade_date in enumerate(trading_dates):
            print(f"处理交易日 {i+1}/{len(trading_dates)}: {trade_date}")
            
            try:
                # 获取当日基础数据
                daily_basic = self.get_daily_basic_data(trade_date)
                daily_price = self.get_daily_price_data(trade_date)
                moneyflow = self.get_moneyflow_data(trade_date)
                
                if daily_basic.empty or daily_price.empty:
                    print(f"  {trade_date} 数据为空，跳过")
                    continue
                
                # 合并数据
                merged_data = daily_price.merge(daily_basic, on=['ts_code', 'trade_date'], how='left')
                
                if not moneyflow.empty:
                    merged_data = merged_data.merge(moneyflow, on=['ts_code', 'trade_date'], how='left')
                
                # 添加股票基础信息
                merged_data = merged_data.merge(stock_basic[['ts_code', 'name']], on='ts_code', how='left')
                
                all_data.append(merged_data)
                
            except Exception as e:
                print(f"  处理{trade_date}数据失败: {e}")
                continue
        
        if all_data:
            # 合并所有数据
            final_data = pd.concat(all_data, ignore_index=True)
            
            # 保存到文件
            output_file = os.path.join(self.cache_folder, f'comprehensive_data_{start_date}_{end_date or datetime.datetime.now().strftime("%Y%m%d")}.csv')
            final_data.to_csv(output_file, index=False, encoding='utf-8-sig')
            
            print(f"✅ 综合数据已保存到: {output_file}")
            print(f"数据行数: {len(final_data)}")
            print(f"数据列数: {len(final_data.columns)}")
            
            return final_data
        else:
            print("❌ 未获取到有效数据")
            return pd.DataFrame()

# 使用示例
if __name__ == "__main__":
    # 创建Tushare数据管理器
    ts_manager = TushareDataManager()
    
    # 缓存综合数据
    data = ts_manager.cache_comprehensive_data(start_date='20241108')
    
    if not data.empty:
        print("\n数据预览:")
        print(data.head())
        print("\n数据列名:")
        print(data.columns.tolist())
