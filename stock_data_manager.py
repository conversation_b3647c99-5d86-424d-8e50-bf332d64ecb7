import datetime
import pandas as pd
from xtquant import xtdata
import tushare as ts
import os
# from joblib import Parallel, delayed  # 暂时不使用并行处理
import warnings
import glob
from pathlib import Path
from download_helper import download_stocks_batch
warnings.filterwarnings("ignore")

class StockDataManager:
    def __init__(self, token='2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211'):
        """
        股票数据管理器
        
        Args:
            token: tushare API token
        """
        self.token = token
        self.pro = ts.pro_api(token)
        self.dividend_type = 'front'  # 前复权
        self.period = '5m'  # 5分钟数据
        self.base_folder = 'data'
        self.cache_folder = os.path.join(self.base_folder, 'cache')
        self.target_times = ['09:35:00', '09:40:00', '09:45:00']  # 目标时间点
        
        # 创建必要的文件夹
        os.makedirs(self.cache_folder, exist_ok=True)
        
    def get_trading_dates(self, start_date, end_date=None):
        """
        获取交易日历
        
        Args:
            start_date: 开始日期 'YYYYMMDD'
            end_date: 结束日期 'YYYYMMDD'，默认为今天
        
        Returns:
            list: 交易日期列表
        """
        if end_date is None:
            end_date = datetime.datetime.now().strftime('%Y%m%d')
            
        try:
            cal = self.pro.trade_cal(exchange='SSE', start_date=start_date, end_date=end_date)
            trading_dates = cal[cal['is_open'] == 1]['cal_date'].tolist()
            return trading_dates
        except Exception as e:
            print(f"获取交易日历失败: {e}")
            return []
    
    def get_stock_list(self, trade_date=None):
        """
        获取股票列表
        
        Args:
            trade_date: 交易日期 'YYYYMMDD'，默认为最近交易日
        
        Returns:
            list: 股票代码列表
        """
        if trade_date is None:
            trade_date = datetime.datetime.now().strftime('%Y%m%d')
            
        try:
            df = self.pro.daily_basic(ts_code='', trade_date=trade_date)
            if df.empty:
                # 如果当天没有数据，尝试获取最近的交易日
                trading_dates = self.get_trading_dates(
                    (datetime.datetime.now() - datetime.timedelta(days=10)).strftime('%Y%m%d')
                )
                if trading_dates:
                    trade_date = trading_dates[-1]
                    df = self.pro.daily_basic(ts_code='', trade_date=trade_date)
            
            stock_list = df['ts_code'].tolist()
            print(f"获取到 {len(stock_list)} 只股票，交易日期: {trade_date}")
            return stock_list
        except Exception as e:
            print(f"获取股票列表失败: {e}")
            return []
    
    def download_data_for_stock(self, stock, period, start_time='20241108'):
        """
        下载单只股票数据

        Args:
            stock: 股票代码
            period: 数据周期
            start_time: 起始时间
        """
        try:
            # 添加时间范围参数，从指定日期开始下载
            xtdata.download_history_data(stock_code=stock, period=period,
                                       start_time=start_time, incrementally=True)
            return True
        except Exception as e:
            print(f"{stock} 数据下载失败: {e}")
            return False
    
    def get_cached_stocks(self):
        """
        获取已缓存的股票列表
        
        Returns:
            set: 已缓存的股票代码集合
        """
        csv_files = glob.glob(os.path.join(self.cache_folder, "*.csv"))
        cached_stocks = set()
        for file in csv_files:
            stock_code = os.path.basename(file).replace('.csv', '')
            cached_stocks.add(stock_code)
        return cached_stocks
    
    def extract_target_time_data(self, df, stock_code):
        """
        提取目标时间点的数据
        
        Args:
            df: 股票数据DataFrame
            stock_code: 股票代码
            
        Returns:
            DataFrame: 包含目标时间点数据的DataFrame
        """
        if df.empty:
            return pd.DataFrame()
            
        # 确保datetime列存在且为索引
        if 'datetime' not in df.columns and 'datetime' not in df.index.names:
            if 'time' in df.columns:
                df['datetime'] = df['time'].apply(lambda x: datetime.datetime.fromtimestamp(x/1000.0))
                df.set_index('datetime', inplace=True)
        elif 'datetime' in df.columns:
            df.set_index('datetime', inplace=True)
            
        # 提取目标时间点数据
        target_data = []
        
        # 按日期分组
        df_grouped = df.groupby(df.index.date)
        
        for date, day_data in df_grouped:
            for target_time in self.target_times:
                # 构造目标时间
                target_datetime = datetime.datetime.combine(date, datetime.time.fromisoformat(target_time))
                
                # 查找最接近的时间点
                time_diff = abs(day_data.index - target_datetime)
                if len(time_diff) > 0:
                    closest_idx = time_diff.idxmin()
                    closest_data = day_data.loc[closest_idx]
                    
                    # 只有在时间差小于3分钟时才认为有效
                    if time_diff.min() <= pd.Timedelta(minutes=3):
                        target_data.append({
                            'date': date,
                            'target_time': target_time,
                            'actual_time': closest_idx.time(),
                            'close': closest_data['close'],
                            'open': closest_data['open'],
                            'high': closest_data['high'],
                            'low': closest_data['low'],
                            'volume': closest_data['volume'],
                            'code': stock_code
                        })
        
        return pd.DataFrame(target_data)

    def update_stock_cache(self, stock_list, force_update=False):
        """
        更新股票缓存数据

        Args:
            stock_list: 股票代码列表
            force_update: 是否强制更新所有数据
        """
        print('开始更新股票缓存数据...')

        # 获取已缓存的股票
        cached_stocks = self.get_cached_stocks() if not force_update else set()

        # 确定需要下载的股票
        stocks_to_download = [stock for stock in stock_list if stock not in cached_stocks]

        if not stocks_to_download:
            print('所有股票数据已缓存，无需下载')
            return

        print(f'需要下载 {len(stocks_to_download)} 只股票数据')

        # 使用批量下载（避免并行处理的pickle问题）
        download_results = download_stocks_batch(stocks_to_download, self.period,
                                                batch_size=100, start_time='20241108')
        results = [result[1] for result in download_results]  # 提取成功标志

        successful_downloads = sum(results)
        print(f'成功下载 {successful_downloads} 只股票数据')

        # 处理并缓存数据
        print('开始处理和缓存数据...')
        for i, stock in enumerate(stocks_to_download):
            if i % 50 == 0:
                print(f'处理进度: {i}/{len(stocks_to_download)} - {stock}')

            try:
                # 获取本地数据
                data = xtdata.get_local_data(
                    field_list=['time', 'open', 'close', 'high', 'low', 'volume',
                               'amount', 'settelementPrice', 'openInterest',
                               'preClose', 'suspendFlag'],
                    stock_list=[stock],
                    period=self.period,
                    dividend_type=self.dividend_type
                )

                if stock in data and not data[stock].empty:
                    df = data[stock].copy()

                    # 处理时间列
                    df['datetime'] = df['time'].apply(lambda x: datetime.datetime.fromtimestamp(x/1000.0))
                    df.set_index('datetime', inplace=True)

                    # 选择需要的列
                    df = df[['open', 'close', 'high', 'low', 'volume', 'amount', 'preClose', 'suspendFlag']]
                    df['code'] = stock

                    # 保存到缓存
                    cache_file = os.path.join(self.cache_folder, f'{stock}.csv')
                    df.to_csv(cache_file)

            except Exception as e:
                print(f'处理股票 {stock} 数据失败: {e}')

        print('数据缓存完成')

    def get_target_time_data_for_all_stocks(self, start_date='20241108'):
        """
        获取所有股票的目标时间点数据

        Args:
            start_date: 开始日期 'YYYYMMDD'

        Returns:
            DataFrame: 包含所有股票目标时间点数据的DataFrame
        """
        print('开始提取目标时间点数据...')

        # 获取股票列表
        stock_list = self.get_stock_list()
        if not stock_list:
            print('无法获取股票列表')
            return pd.DataFrame()

        # 更新缓存
        self.update_stock_cache(stock_list)

        # 读取缓存数据并提取目标时间点数据
        all_target_data = []
        cached_stocks = self.get_cached_stocks()

        print(f'开始处理 {len(cached_stocks)} 只股票的目标时间点数据...')

        for i, stock in enumerate(cached_stocks):
            if i % 100 == 0:
                print(f'提取进度: {i}/{len(cached_stocks)} - {stock}')

            try:
                cache_file = os.path.join(self.cache_folder, f'{stock}.csv')
                if os.path.exists(cache_file):
                    df = pd.read_csv(cache_file, index_col=0, parse_dates=True)

                    # 过滤日期
                    start_datetime = datetime.datetime.strptime(start_date, '%Y%m%d')
                    df = df[df.index >= start_datetime]

                    if not df.empty:
                        target_data = self.extract_target_time_data(df, stock)
                        if not target_data.empty:
                            all_target_data.append(target_data)

            except Exception as e:
                print(f'处理股票 {stock} 目标时间点数据失败: {e}')

        if all_target_data:
            result_df = pd.concat(all_target_data, ignore_index=True)

            # 保存结果
            output_file = os.path.join(self.base_folder, f'target_time_data_{start_date}.csv')
            result_df.to_csv(output_file, index=False)
            print(f'目标时间点数据已保存到: {output_file}')

            return result_df
        else:
            print('未找到有效的目标时间点数据')
            return pd.DataFrame()

# 使用示例
if __name__ == "__main__":
    # 创建数据管理器
    manager = StockDataManager()

    # 获取目标时间点数据（从20241108开始）
    target_data = manager.get_target_time_data_for_all_stocks(start_date='20241108')

    if not target_data.empty:
        print(f"成功提取 {len(target_data)} 条目标时间点数据")
        print("\n数据预览:")
        print(target_data.head(10))

        # 按股票和时间点统计
        print("\n按目标时间点统计:")
        print(target_data.groupby('target_time').size())
    else:
        print("未获取到数据")
