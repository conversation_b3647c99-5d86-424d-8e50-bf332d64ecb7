"""
股票数据管理器使用示例
"""
from stock_data_manager import StockDataManager
import pandas as pd
import datetime

def main():
    """主函数示例"""
    # 创建数据管理器实例
    print("=== 股票数据管理器使用示例 ===\n")
    
    manager = StockDataManager()
    
    # 示例1: 获取交易日历
    print("1. 获取交易日历")
    trading_dates = manager.get_trading_dates('20241101', '20241130')
    print(f"2024年11月交易日数量: {len(trading_dates)}")
    print(f"最近5个交易日: {trading_dates[-5:]}\n")
    
    # 示例2: 获取股票列表
    print("2. 获取股票列表")
    stock_list = manager.get_stock_list()
    print(f"当前股票数量: {len(stock_list)}")
    print(f"前10只股票: {stock_list[:10]}\n")
    
    # 示例3: 检查缓存状态
    print("3. 检查缓存状态")
    cached_stocks = manager.get_cached_stocks()
    print(f"已缓存股票数量: {len(cached_stocks)}")
    if cached_stocks:
        print(f"已缓存股票示例: {list(cached_stocks)[:5]}\n")
    else:
        print("暂无缓存数据\n")
    
    # 示例4: 获取目标时间点数据
    print("4. 获取目标时间点数据")
    print("开始获取从20241108开始的目标时间点数据...")
    
    target_data = manager.get_target_time_data_for_all_stocks(start_date='20241108')
    
    if not target_data.empty:
        print(f"✅ 成功获取 {len(target_data)} 条目标时间点数据")
        
        # 数据统计
        print("\n📊 数据统计:")
        print(f"涉及股票数量: {target_data['code'].nunique()}")
        print(f"数据日期范围: {target_data['date'].min()} 到 {target_data['date'].max()}")
        
        # 按时间点统计
        print("\n⏰ 按目标时间点统计:")
        time_stats = target_data.groupby('target_time').agg({
            'code': 'count',
            'close': ['mean', 'std']
        }).round(2)
        print(time_stats)
        
        # 数据预览
        print("\n📋 数据预览 (前10条):")
        preview_cols = ['date', 'code', 'target_time', 'actual_time', 'close']
        print(target_data[preview_cols].head(10).to_string(index=False))
        
        # 保存特定格式的数据
        save_formatted_data(target_data)
        
    else:
        print("❌ 未获取到数据，请检查网络连接和API配置")

def save_formatted_data(target_data):
    """保存格式化的数据"""
    print("\n5. 保存格式化数据")
    
    # 按日期和时间点重新组织数据
    pivot_data = target_data.pivot_table(
        index=['date', 'code'], 
        columns='target_time', 
        values='close',
        aggfunc='first'
    ).reset_index()
    
    # 重命名列
    pivot_data.columns.name = None
    if '09:35:00' in pivot_data.columns:
        pivot_data.rename(columns={'09:35:00': 'close_0935'}, inplace=True)
    if '09:40:00' in pivot_data.columns:
        pivot_data.rename(columns={'09:40:00': 'close_0940'}, inplace=True)
    if '09:45:00' in pivot_data.columns:
        pivot_data.rename(columns={'09:45:00': 'close_0945'}, inplace=True)
    
    # 保存到文件
    output_file = f"data/formatted_target_data_{datetime.datetime.now().strftime('%Y%m%d')}.csv"
    pivot_data.to_csv(output_file, index=False)
    print(f"✅ 格式化数据已保存到: {output_file}")
    
    # 显示格式化数据预览
    print("\n📋 格式化数据预览:")
    print(pivot_data.head().to_string(index=False))

def analyze_target_time_data(file_path):
    """分析目标时间点数据"""
    print(f"\n=== 分析目标时间点数据: {file_path} ===")
    
    try:
        df = pd.read_csv(file_path)
        
        # 基本统计
        print(f"数据行数: {len(df)}")
        print(f"股票数量: {df['code'].nunique()}")
        print(f"日期数量: {df['date'].nunique()}")
        
        # 时间点覆盖率
        time_columns = [col for col in df.columns if 'close_' in col]
        print(f"\n时间点覆盖率:")
        for col in time_columns:
            coverage = (df[col].notna().sum() / len(df)) * 100
            print(f"{col}: {coverage:.1f}%")
        
        # 价格统计
        print(f"\n价格统计:")
        for col in time_columns:
            if col in df.columns:
                stats = df[col].describe()
                print(f"{col}: 均值={stats['mean']:.2f}, 标准差={stats['std']:.2f}")
                
    except Exception as e:
        print(f"分析数据失败: {e}")

if __name__ == "__main__":
    main()
    
    # 可选: 分析已保存的数据
    # analyze_target_time_data("data/formatted_target_data_20241108.csv")
