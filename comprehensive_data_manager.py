"""
综合数据管理器 - 整合xtquant和tushare数据
"""
import pandas as pd
import os
import datetime
from stock_data_manager import StockDataManager
from tushare_data_manager import TushareDataManager
import warnings
warnings.filterwarnings("ignore")

class ComprehensiveDataManager:
    def __init__(self, token='2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211'):
        """
        综合数据管理器
        
        Args:
            token: tushare API token
        """
        self.xt_manager = StockDataManager(token)
        self.ts_manager = TushareDataManager(token)
        self.output_folder = 'data/comprehensive'
        os.makedirs(self.output_folder, exist_ok=True)
        
    def create_comprehensive_dataset(self, start_date='20241108'):
        """
        创建综合数据集
        
        Args:
            start_date: 开始日期 'YYYYMMDD'
            
        Returns:
            DataFrame: 综合数据集
        """
        print("=== 开始创建综合数据集 ===\n")
        
        # 1. 获取xtquant目标时间点数据
        print("1. 获取xtquant目标时间点数据...")
        xt_data = self.xt_manager.get_target_time_data_for_all_stocks(start_date=start_date)
        
        if xt_data.empty:
            print("❌ 未获取到xtquant数据")
            return pd.DataFrame()
            
        print(f"✅ 获取到 {len(xt_data)} 条xtquant数据")
        
        # 2. 获取tushare综合数据
        print("\n2. 获取tushare综合数据...")
        ts_data = self.ts_manager.cache_comprehensive_data(start_date=start_date)
        
        if ts_data.empty:
            print("❌ 未获取到tushare数据")
            return pd.DataFrame()
            
        print(f"✅ 获取到 {len(ts_data)} 条tushare数据")
        
        # 3. 数据预处理和合并
        print("\n3. 数据预处理和合并...")
        comprehensive_data = self.merge_datasets(xt_data, ts_data)
        
        if comprehensive_data.empty:
            print("❌ 数据合并失败")
            return pd.DataFrame()
            
        # 4. 保存综合数据
        print("\n4. 保存综合数据...")
        output_file = os.path.join(self.output_folder, f'comprehensive_dataset_{start_date}.csv')
        comprehensive_data.to_csv(output_file, index=False, encoding='utf-8-sig')
        
        print(f"✅ 综合数据集已保存到: {output_file}")
        print(f"数据行数: {len(comprehensive_data)}")
        print(f"数据列数: {len(comprehensive_data.columns)}")
        
        return comprehensive_data
    
    def merge_datasets(self, xt_data, ts_data):
        """
        合并xtquant和tushare数据
        
        Args:
            xt_data: xtquant数据
            ts_data: tushare数据
            
        Returns:
            DataFrame: 合并后的数据
        """
        try:
            # 预处理xtquant数据
            xt_processed = self.preprocess_xt_data(xt_data)
            
            # 预处理tushare数据
            ts_processed = self.preprocess_ts_data(ts_data)
            
            # 合并数据
            merged_data = xt_processed.merge(
                ts_processed, 
                left_on=['code', 'date_str'], 
                right_on=['ts_code', 'trade_date'], 
                how='inner'
            )
            
            # 清理和重命名列
            final_data = self.clean_and_rename_columns(merged_data)
            
            return final_data
            
        except Exception as e:
            print(f"数据合并失败: {e}")
            return pd.DataFrame()
    
    def preprocess_xt_data(self, xt_data):
        """
        预处理xtquant数据
        
        Args:
            xt_data: xtquant原始数据
            
        Returns:
            DataFrame: 预处理后的数据
        """
        # 转换日期格式
        xt_data['date_str'] = pd.to_datetime(xt_data['date']).dt.strftime('%Y%m%d')
        
        # 按股票和日期聚合，创建透视表
        pivot_data = xt_data.pivot_table(
            index=['code', 'date', 'date_str'],
            columns='target_time',
            values=['close', 'open', 'high', 'low', 'volume'],
            aggfunc='first'
        ).reset_index()
        
        # 扁平化列名
        pivot_data.columns = [f"{col[0]}_{col[1].replace(':', '')}" if col[1] else col[0] 
                             for col in pivot_data.columns]
        
        return pivot_data
    
    def preprocess_ts_data(self, ts_data):
        """
        预处理tushare数据
        
        Args:
            ts_data: tushare原始数据
            
        Returns:
            DataFrame: 预处理后的数据
        """
        # 确保必要的列存在
        required_columns = ['ts_code', 'trade_date', 'name']
        for col in required_columns:
            if col not in ts_data.columns:
                ts_data[col] = None
                
        return ts_data
    
    def clean_and_rename_columns(self, merged_data):
        """
        清理和重命名列
        
        Args:
            merged_data: 合并后的原始数据
            
        Returns:
            DataFrame: 清理后的数据
        """
        # 定义列名映射
        column_mapping = {
            'ts_code': '股票代码',
            'name': '股票名称', 
            'trade_date': '交易日期',
            'open': '开盘价',
            'high': '最高价',
            'low': '最低价',
            'close': '收盘价',
            'pre_close': '前收盘价',
            'vol': '成交量',
            'amount': '成交额',
            'circ_mv': '流通市值',
            'total_mv': '总市值',
            'close_093500': '09:35收盘价',
            'close_094000': '09:40收盘价', 
            'close_094500': '09:45收盘价',
            'open_093500': '09:35开盘价',
            'open_094000': '09:40开盘价',
            'open_094500': '09:45开盘价',
            'high_093500': '09:35最高价',
            'high_094000': '09:40最高价',
            'high_094500': '09:45最高价',
            'low_093500': '09:35最低价',
            'low_094000': '09:40最低价',
            'low_094500': '09:45最低价',
            'volume_093500': '09:35成交量',
            'volume_094000': '09:40成交量',
            'volume_094500': '09:45成交量',
            'buy_sm_amount': '散户资金买入额',
            'sell_sm_amount': '散户资金卖出额',
            'buy_md_amount': '中户资金买入额',
            'sell_md_amount': '中户资金卖出额',
            'buy_lg_amount': '大户资金买入额',
            'sell_lg_amount': '大户资金卖出额',
            'buy_elg_amount': '机构资金买入额',
            'sell_elg_amount': '机构资金卖出额'
        }
        
        # 选择需要的列
        available_columns = [col for col in column_mapping.keys() if col in merged_data.columns]
        selected_data = merged_data[available_columns].copy()
        
        # 重命名列
        selected_data.rename(columns=column_mapping, inplace=True)
        
        # 添加缺失的列（用空值填充）
        target_columns = [
            '股票代码', '股票名称', '交易日期', '开盘价', '最高价', '最低价', '收盘价', '前收盘价',
            '成交量', '成交额', '流通市值', '总市值', '净利润TTM', '现金流TTM', '净资产', '总资产', '总负债',
            '净利润(当季)', '中户资金买入额', '中户资金卖出额', '大户资金买入额', '大户资金卖出额',
            '散户资金买入额', '散户资金卖出额', '机构资金买入额', '机构资金卖出额',
            '沪深300成分股', '上证50成分股', '中证500成分股', '中证1000成分股', '中证2000成分股', '创业板指成分股',
            '新版申万一级行业名称', '新版申万二级行业名称', '新版申万三级行业名称',
            '09:35收盘价', '09:40收盘价', '09:45收盘价'
        ]
        
        for col in target_columns:
            if col not in selected_data.columns:
                selected_data[col] = None
                
        # 重新排序列
        selected_data = selected_data[target_columns]
        
        return selected_data
    
    def generate_summary_report(self, data):
        """
        生成数据摘要报告
        
        Args:
            data: 综合数据集
            
        Returns:
            dict: 摘要报告
        """
        if data.empty:
            return {}
            
        report = {
            '数据概况': {
                '总行数': len(data),
                '股票数量': data['股票代码'].nunique() if '股票代码' in data.columns else 0,
                '交易日数量': data['交易日期'].nunique() if '交易日期' in data.columns else 0,
                '数据列数': len(data.columns)
            },
            '时间范围': {
                '开始日期': data['交易日期'].min() if '交易日期' in data.columns else None,
                '结束日期': data['交易日期'].max() if '交易日期' in data.columns else None
            },
            '数据完整性': {}
        }
        
        # 检查关键列的完整性
        key_columns = ['09:35收盘价', '09:40收盘价', '09:45收盘价', '收盘价', '成交量']
        for col in key_columns:
            if col in data.columns:
                completeness = (data[col].notna().sum() / len(data)) * 100
                report['数据完整性'][col] = f"{completeness:.1f}%"
        
        return report

# 使用示例
if __name__ == "__main__":
    # 创建综合数据管理器
    manager = ComprehensiveDataManager()
    
    # 创建综合数据集
    comprehensive_data = manager.create_comprehensive_dataset(start_date='20241108')
    
    if not comprehensive_data.empty:
        # 生成摘要报告
        report = manager.generate_summary_report(comprehensive_data)
        
        print("\n=== 数据摘要报告 ===")
        for section, content in report.items():
            print(f"\n{section}:")
            for key, value in content.items():
                print(f"  {key}: {value}")
        
        print("\n=== 数据预览 ===")
        print(comprehensive_data.head())
    else:
        print("❌ 未能创建综合数据集")
